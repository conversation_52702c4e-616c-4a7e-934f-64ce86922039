from Crypto.Cipher import AES
import sys

KEY = b'bc5!@#89bc5!@#89'
IV = b'1234567890abcdef'
OFFSET = 8

def decrypt_zte(encfile, outfile):
    with open(encfile, 'rb') as f:
        data = f.read()

    cipher = AES.new(KEY, AES.MODE_CBC, IV)
    decrypted = cipher.decrypt(data[OFFSET:])

    # Remove PKCS#7 padding
    pad_len = decrypted[-1]
    if pad_len > 0 and pad_len <= 16:
        decrypted = decrypted[:-pad_len]

    with open(outfile, 'wb') as f:
        f.write(decrypted)

    print(f"[+] Decrypted {encfile} -> {outfile}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print(f"Usage: python {sys.argv[0]} input.enc output.xml")
        sys.exit(1)
    decrypt_zte(sys.argv[1], sys.argv[2])
