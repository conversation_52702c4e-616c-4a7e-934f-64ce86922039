Step 1 — Check if sendcmd exists
Inside your telnet shell:

sh
Copy
Edit
ls /bin/sendcmd
ls /sbin/sendcmd
ls /usr/bin/sendcmd
ls /usr/sbin/sendcmd
If it exists, check its help:

sh
Copy
Edit
sendcmd 1
It should output something like:

php-template
Copy
Edit
sendcmd <slotid> <command> <params...>
Step 2 — Dump the decrypted config
If sendcmd is there, try:

sh
Copy
Edit
sendcmd 1 DB decry /userconfig/cfg/db_user_cfg.xml; echo $?

sendcmd 1 DB decry /userconfig/cfg/db_backup_cfg.xml; echo $?

sendcmd 1 DB decry /userconfig/cfg/config.xml; echo $?

sendcmd 1 DB decry /userconfig/cfg/db_user_cfg.xml; echo $?

sendcmd 1 DB decry /userconfig/cfg/db_user_cfg_decrypted.xml; echo $?

If successful, you’ll get the decrypted file in:

bash
Copy
Edit
/var/tmp/debug-decry-cfg

file size 146060 sep 6 10:21 
Step 3 — Transfer the decrypted config
You can:

sh
Copy
Edit
cat /var/tmp/debug-decry-cfg
Copy‑paste to your PC, or use tftp/wget to send it to a local server.

------------

ls -lt /tmp
ls -lt /var/tmp
ls -lt /userconfig/cfg

------

sendcmd 1 DB decry /userconfig/cfg/config.xml

sendcmd 1 DB decry /userconfig/cfg/db_user_cfg.xml

sendcmd 1 DB decry /userconfig/cfg/db_backup_cfg.xml

-----

cd /userconfig/cfg

tftp -g -r db_user_cfg.xml 192.168.11.100


tftp -g -r db_backup_cfg.xml 192.168.11.100


-----------

factorymode_crack.exe -l xxx open -i 192.168.11.1 -u support -pw Orange9CC2
